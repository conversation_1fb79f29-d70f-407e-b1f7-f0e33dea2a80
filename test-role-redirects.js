/**
 * Role-Based Redirect Testing Script
 * Tests authentication and role-based redirects
 */

const fetch = globalThis.fetch || require('node-fetch');

const BACKEND_URL = 'http://localhost:8080';
const FRONTEND_URL = 'http://localhost:3008';

// Test users for different roles
const testUsers = {
  buyer: {
    name: 'Test Buyer',
    email: `buyer-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    role: 'buyer',
    phone: `98765${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`
  },
  broker: {
    name: 'Test Broker',
    email: `broker-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    role: 'broker',
    phone: `98765${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`
  },
  contractor: {
    name: 'Test Contractor',
    email: `contractor-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    role: 'contractor',
    phone: `98765${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`
  },
  admin: {
    name: 'Test Admin',
    email: `admin-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    role: 'admin',
    phone: `98765${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`
  }
};

async function registerUser(user) {
  try {
    const response = await fetch(`${BACKEND_URL}/user-service/api/v1/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(user)
    });

    if (response.ok) {
      console.log(`   ✅ ${user.role} registered successfully`);
      return true;
    } else {
      const error = await response.text();
      console.log(`   ❌ ${user.role} registration failed: ${error}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ ${user.role} registration error: ${error.message}`);
    return false;
  }
}

async function testFrontendLogin(user) {
  try {
    const response = await fetch(`${FRONTEND_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: user.email,
        password: user.password
      })
    });

    if (response.ok) {
      const data = await response.json();
      const cookies = response.headers.get('set-cookie');
      
      console.log(`   ✅ ${user.role} frontend login successful`);
      console.log(`   📄 Success: ${data.success}`);
      console.log(`   🍪 Cookies: ${cookies ? 'Set' : 'Not set'}`);
      
      return { success: true, data, cookies };
    } else {
      const error = await response.text();
      console.log(`   ❌ ${user.role} frontend login failed: ${error}`);
      return { success: false };
    }
  } catch (error) {
    console.log(`   ❌ ${user.role} frontend login error: ${error.message}`);
    return { success: false };
  }
}

async function testDashboardAccess(role) {
  const dashboardUrls = {
    buyer: `${FRONTEND_URL}/dashboard`,
    broker: `${FRONTEND_URL}/broker/dashboard`,
    contractor: `${FRONTEND_URL}/contractor/dashboard`,
    admin: `${FRONTEND_URL}/admin/dashboard`
  };

  const url = dashboardUrls[role];
  if (!url) {
    console.log(`   ⚠️  No dashboard URL configured for role: ${role}`);
    return false;
  }

  try {
    const response = await fetch(url);
    
    if (response.ok) {
      const content = await response.text();
      const hasRoleContent = content.includes(`${role.charAt(0).toUpperCase() + role.slice(1)} Dashboard`);
      
      console.log(`   ✅ ${role} dashboard accessible`);
      console.log(`   📄 Contains role content: ${hasRoleContent ? 'Yes' : 'No'}`);
      
      return true;
    } else {
      console.log(`   ❌ ${role} dashboard not accessible: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ ${role} dashboard access error: ${error.message}`);
    return false;
  }
}

async function testRoleRedirect(role) {
  console.log(`\n🔄 Testing ${role.toUpperCase()} Role Redirect...`);
  
  const user = testUsers[role];
  
  // Step 1: Register user
  console.log(`📝 Registering ${role} user`);
  const registrationSuccess = await registerUser(user);
  
  if (!registrationSuccess) {
    console.log(`   ⚠️  Skipping ${role} tests due to registration failure`);
    return { success: false, step: 'registration' };
  }

  // Step 2: Test frontend login
  console.log(`🔑 Testing ${role} frontend login`);
  const loginResult = await testFrontendLogin(user);
  
  if (!loginResult.success) {
    console.log(`   ⚠️  Skipping ${role} dashboard test due to login failure`);
    return { success: false, step: 'login' };
  }

  // Step 3: Test dashboard access
  console.log(`🏠 Testing ${role} dashboard access`);
  const dashboardSuccess = await testDashboardAccess(role);
  
  return {
    success: registrationSuccess && loginResult.success && dashboardSuccess,
    step: 'complete',
    user: user.email
  };
}

async function testAllRoles() {
  console.log('🚀 Role-Based Redirect Testing');
  console.log('==============================');
  console.log(`🔧 Backend: ${BACKEND_URL}`);
  console.log(`🌐 Frontend: ${FRONTEND_URL}`);
  
  const results = {};
  
  // Test each role
  for (const role of Object.keys(testUsers)) {
    results[role] = await testRoleRedirect(role);
  }
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  
  for (const [role, result] of Object.entries(results)) {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const step = result.success ? 'Complete' : `Failed at: ${result.step}`;
    console.log(`${role.toUpperCase().padEnd(12)} ${status} - ${step}`);
    if (result.user) {
      console.log(`${' '.repeat(15)} User: ${result.user}`);
    }
  }
  
  const passCount = Object.values(results).filter(r => r.success).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n🎯 Overall Result: ${passCount}/${totalCount} roles working correctly`);
  
  if (passCount === totalCount) {
    console.log('\n🎉 All role-based redirects are working!');
    console.log('💡 You can now test manually in the browser:');
    console.log('   1. Go to: http://localhost:3008/auth/login');
    console.log('   2. Login with any of the test users above');
    console.log('   3. Verify you are redirected to the correct dashboard');
  } else {
    console.log('\n⚠️  Some role redirects are not working correctly');
    console.log('💡 Check the error messages above for details');
  }
  
  console.log('\n📋 Manual Testing Checklist:');
  console.log('□ Test buyer login → /dashboard');
  console.log('□ Test broker login → /broker/dashboard');
  console.log('□ Test contractor login → /contractor/dashboard');
  console.log('□ Test admin login → /admin/dashboard');
  console.log('□ Test protected route access');
  console.log('□ Test logout functionality');
  
  return results;
}

// Additional utility functions for manual testing
async function testProtectedRoutes() {
  console.log('\n🔒 Testing Protected Routes...');
  
  const protectedRoutes = [
    '/dashboard',
    '/broker/dashboard',
    '/contractor/dashboard',
    '/admin/dashboard',
    '/brokers'
  ];
  
  for (const route of protectedRoutes) {
    try {
      const response = await fetch(`${FRONTEND_URL}${route}`);
      console.log(`${route.padEnd(25)} Status: ${response.status}`);
    } catch (error) {
      console.log(`${route.padEnd(25)} Error: ${error.message}`);
    }
  }
}

async function main() {
  await testAllRoles();
  await testProtectedRoutes();
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.log('Unhandled Rejection at:', promise, 'reason:', reason);
});

main().catch(console.error);
